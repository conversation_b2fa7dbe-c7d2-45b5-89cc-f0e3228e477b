import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:du_an_flutter/constants/colors.dart';
import 'package:du_an_flutter/model/activity_item.dart';
import 'package:du_an_flutter/l10n/app_localizations.dart';

class HomeScreenApprover extends StatefulWidget {
  final VoidCallback? onNavigateToHome;
  final List<ActivityItem>? activityItems;

  const HomeScreenApprover({
    super.key,
    this.onNavigateToHome,
    this.activityItems,
  });

  @override
  State<HomeScreenApprover> createState() => _HomeScreenApproverState();
}

// Data models for different chart types
class FinancialData {
  FinancialData(this.period, this.income, this.expense);
  final String period;
  final double income;
  final double expense;
}

// Data model for HomeScreen preview items
class HomeScreenItem {
  HomeScreenItem({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.amount,
    required this.status,
    required this.date,
  });

  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final double? amount;
  final String status;
  final String date;
}

class _HomeScreenApproverState extends State<HomeScreenApprover> {
  bool _isLoading = false;
  String _selectedPeriod =
      'Tháng'; // Internal value, will be mapped to localized display

  // Account balance (hardcoded for now)
  final double _accountBalance = *********; // ₫125,000,000

  // Secondary date selectors
  int _selectedMonth = DateTime.now().month > 1
      ? DateTime.now().month - 1
      : 1; // Default to most recent completed month
  int _selectedYear = DateTime.now().year; // For month and quarter view

  // Available options for secondary selectors
  List<int> get _availableMonths {
    // For all periods, only show completed months (not current ongoing month)
    final currentMonth = DateTime.now().month;
    return currentMonth > 1
        ? List.generate(currentMonth - 1, (index) => index + 1)
        : []; // If current month is January, no completed months
  }

  final List<int> _availableYears = [2022, 2023, 2024, 2025];

  // Helper methods to map between internal and localized values
  String _getLocalizedPeriod(String internalPeriod) {
    switch (internalPeriod) {
      case 'Tuần':
        return AppLocalizations.of(context).week;
      case 'Tháng':
        return AppLocalizations.of(context).month;
      case 'Quý':
        return AppLocalizations.of(context).quarter;
      case 'Năm':
        return AppLocalizations.of(context).year;
      default:
        return internalPeriod;
    }
  }

  String _getInternalPeriod(String localizedPeriod) {
    if (localizedPeriod == AppLocalizations.of(context).week) return 'Tuần';
    if (localizedPeriod == AppLocalizations.of(context).month) return 'Tháng';
    if (localizedPeriod == AppLocalizations.of(context).quarter) return 'Quý';
    if (localizedPeriod == AppLocalizations.of(context).year) return 'Năm';
    return localizedPeriod;
  }

  // Helper method to get localized status strings
  String _getLocalizedStatus(String status) {
    switch (status) {
      case 'Đã duyệt':
        return AppLocalizations.of(context).statusApproved;
      case 'Chờ duyệt':
        return AppLocalizations.of(context).statusPendingApprover;
      case 'Chờ kế toán':
        return AppLocalizations.of(context).statusPendingAccountant;
      case 'Từ chối':
        return AppLocalizations.of(context).statusFailed;
      case 'Nháp':
        return AppLocalizations.of(context).statusDraft;
      case 'Hủy':
        return AppLocalizations.of(context).statusCancelled;
      default:
        return status;
    }
  }

  // Mock data for financial overview (Thu vào / Chi ra)
  List<FinancialData> get _financialData {
    switch (_selectedPeriod) {
      case 'Tuần':
        return _getWeeklyData(_selectedMonth);
      case 'Quý':
        return _getQuarterlyData(_selectedYear);
      case 'Năm':
        return _getYearlyData();
      default: // Tháng
        return _getMonthlyData(_selectedYear);
    }
  }

  // Get weekly data for specific month
  List<FinancialData> _getWeeklyData(int month) {
    // Get localized week labels
    final week1 = AppLocalizations.of(context).week1;
    final week2 = AppLocalizations.of(context).week2;
    final week3 = AppLocalizations.of(context).week3;
    final week4 = AppLocalizations.of(context).week4;

    // Mock data varies by month to show realistic seasonal patterns
    final Map<int, List<FinancialData>> weeklyDataByMonth = {
      1: [
        // January - Post-holiday lower activity
        FinancialData(week1, 8500000, 6200000),
        FinancialData(week2, 12800000, 8500000),
        FinancialData(week3, 15200000, 9800000),
        FinancialData(week4, 13600000, 8900000),
      ],
      2: [
        // February - Valentine's boost
        FinancialData(week1, 14200000, 9200000),
        FinancialData(week2, 18500000, 11800000),
        FinancialData(week3, 16800000, 10500000),
        FinancialData(week4, 15200000, 9800000),
      ],
      3: [
        // March - Spring activity increase
        FinancialData(week1, 16500000, 10800000),
        FinancialData(week2, 19200000, 12500000),
        FinancialData(week3, 21800000, 14200000),
        FinancialData(week4, 18900000, 12100000),
      ],
      4: [
        // April - Steady growth
        FinancialData(week1, 17800000, 11500000),
        FinancialData(week2, 20500000, 13200000),
        FinancialData(week3, 22100000, 14800000),
        FinancialData(week4, 19600000, 12800000),
      ],
      5: [
        // May - Peak season
        FinancialData(week1, 22500000, 14500000),
        FinancialData(week2, 25800000, 16200000),
        FinancialData(week3, 28200000, 17800000),
        FinancialData(week4, 24600000, 15500000),
      ],
      6: [
        // June - Summer start
        FinancialData(week1, 21200000, 13800000),
        FinancialData(week2, 24500000, 15200000),
        FinancialData(week3, 26800000, 16800000),
        FinancialData(week4, 23400000, 14900000),
      ],
      7: [
        // July - Summer peak
        FinancialData(week1, 25600000, 16200000),
        FinancialData(week2, 28900000, 18200000),
        FinancialData(week3, 31200000, 19800000),
        FinancialData(week4, 27800000, 17500000),
      ],
      8: [
        // August - Continued summer activity
        FinancialData(week1, 24800000, 15800000),
        FinancialData(week2, 27200000, 17200000),
        FinancialData(week3, 29500000, 18500000),
        FinancialData(week4, 26100000, 16500000),
      ],
      9: [
        // September - Back to school/work
        FinancialData(week1, 23500000, 15200000),
        FinancialData(week2, 26800000, 16800000),
        FinancialData(week3, 28200000, 17800000),
        FinancialData(week4, 25400000, 16200000),
      ],
      10: [
        // October - Autumn activity
        FinancialData(week1, 22100000, 14500000),
        FinancialData(week2, 25200000, 15800000),
        FinancialData(week3, 27800000, 17200000),
        FinancialData(week4, 24600000, 15500000),
      ],
      11: [
        // November - Pre-holiday preparation
        FinancialData(week1, 26800000, 16800000),
        FinancialData(week2, 29500000, 18500000),
        FinancialData(week3, 32200000, 20200000),
        FinancialData(week4, 28900000, 18200000),
      ],
      12: [
        // December - Holiday season peak
        FinancialData(week1, 31500000, 19800000),
        FinancialData(week2, 35200000, 22200000),
        FinancialData(week3, 38800000, 24500000),
        FinancialData(week4, 33600000, 21200000),
      ],
    };

    return weeklyDataByMonth[month] ?? weeklyDataByMonth[1]!;
  }

  // Get monthly data for specific year
  List<FinancialData> _getMonthlyData(int year) {
    final Map<int, List<FinancialData>> monthlyDataByYear = {
      2022: [
        FinancialData('T1', 42200000, 26500000),
        FinancialData('T2', 38900000, 24200000),
        FinancialData('T3', 48800000, 30100000),
        FinancialData('T4', 45600000, 28800000),
        FinancialData('T5', 52300000, 32200000),
        FinancialData('T6', 49700000, 30800000),
        FinancialData('T7', 56200000, 34500000),
        FinancialData('T8', 53800000, 33200000),
        FinancialData('T9', 51400000, 31800000),
        FinancialData('T10', 48900000, 30200000),
        FinancialData('T11', 55600000, 34200000),
        FinancialData('T12', 62100000, 38200000),
      ],
      2023: [
        FinancialData('T1', 45200000, 28500000),
        FinancialData('T2', 41900000, 26200000),
        FinancialData('T3', 52800000, 33100000),
        FinancialData('T4', 49600000, 30800000),
        FinancialData('T5', 56300000, 35200000),
        FinancialData('T6', 53700000, 33800000),
        FinancialData('T7', 60200000, 37500000),
        FinancialData('T8', 57800000, 36200000),
        FinancialData('T9', 55400000, 34800000),
        FinancialData('T10', 52900000, 33200000),
        FinancialData('T11', 59600000, 37200000),
        FinancialData('T12', 66100000, 41200000),
      ],
      2024: [
        FinancialData('T1', 48200000, 30500000),
        FinancialData('T2', 44900000, 28200000),
        FinancialData('T3', 56800000, 35100000),
        FinancialData('T4', 53600000, 32800000),
        FinancialData('T5', 60300000, 37200000),
        FinancialData('T6', 57700000, 35800000),
        FinancialData('T7', 64200000, 39500000),
        FinancialData('T8', 61800000, 38200000),
        FinancialData('T9', 59400000, 36800000),
        FinancialData('T10', 56900000, 35200000),
        FinancialData('T11', 63600000, 39200000),
        FinancialData('T12', 70100000, 43200000),
      ],
      2025: [
        FinancialData('T1', 51200000, 32500000),
        FinancialData('T2', 47900000, 30200000),
        FinancialData('T3', 60800000, 37100000),
        FinancialData('T4', 57600000, 34800000),
        FinancialData('T5', 64300000, 39200000),
        FinancialData('T6', 61700000, 37800000),
        FinancialData('T7', 68200000, 41500000),
        FinancialData('T8', 65800000, 40200000),
        FinancialData('T9', 63400000, 38800000),
        FinancialData('T10', 60900000, 37200000),
        FinancialData('T11', 67600000, 41200000),
        FinancialData('T12', 74100000, 45200000),
      ],
    };

    final fullYearData = monthlyDataByYear[year] ?? monthlyDataByYear[2024]!;

    // For current year, only show completed months
    if (year == DateTime.now().year) {
      final currentMonth = DateTime.now().month;
      final completedMonthsCount = currentMonth > 1 ? currentMonth - 1 : 0;

      if (completedMonthsCount == 0) {
        return []; // No completed months in current year
      }

      return fullYearData.take(completedMonthsCount).toList();
    }

    // For previous years, show all months
    return fullYearData;
  }

  // Get quarterly data for specific year
  List<FinancialData> _getQuarterlyData(int year) {
    final Map<int, List<FinancialData>> quarterlyDataByYear = {
      2022: [
        FinancialData('Q1', 129900000, 80800000),
        FinancialData('Q2', 147600000, 91800000),
        FinancialData('Q3', 161400000, 99500000),
        FinancialData('Q4', 166600000, 102600000),
      ],
      2023: [
        FinancialData('Q1', 139900000, 87800000),
        FinancialData('Q2', 159600000, 99800000),
        FinancialData('Q3', 173400000, 108500000),
        FinancialData('Q4', 178600000, 111600000),
      ],
      2024: [
        FinancialData('Q1', 149900000, 93800000),
        FinancialData('Q2', 171600000, 105800000),
        FinancialData('Q3', 185400000, 114500000),
        FinancialData('Q4', 190600000, 117600000),
      ],
      2025: [
        FinancialData('Q1', 159900000, 99800000),
        FinancialData('Q2', 183600000, 111800000),
        FinancialData('Q3', 197400000, 120500000),
        FinancialData('Q4', 202600000, 123600000),
      ],
    };

    final fullYearData =
        quarterlyDataByYear[year] ?? quarterlyDataByYear[2024]!;

    // For current year, only show completed quarters
    if (year == DateTime.now().year) {
      final currentMonth = DateTime.now().month;
      final currentQuarter =
          ((currentMonth - 1) ~/ 3) + 1; // Calculate current quarter (1-4)
      final completedQuartersCount =
          currentQuarter > 1 ? currentQuarter - 1 : 0;

      if (completedQuartersCount == 0) {
        return []; // No completed quarters in current year
      }

      return fullYearData.take(completedQuartersCount).toList();
    }

    // For previous years, show all quarters
    return fullYearData;
  }

  // Get yearly data (no secondary selector needed)
  List<FinancialData> _getYearlyData() {
    return [
      FinancialData('2022', 605500000, 374700000),
      FinancialData('2023', 651500000, 407700000),
      FinancialData('2024', 697500000, 431700000),
      FinancialData('2025', 743500000, 455700000),
    ];
  }

  // Calculate total income and expense
  double get _totalIncome {
    return _financialData.fold(0, (sum, data) => sum + data.income);
  }

  double get _totalExpense {
    return _financialData.fold(0, (sum, data) => sum + data.expense);
  }

  String _formatFullCurrency(double amount) {
    return 'đ${amount.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]}.',
        )}';
  }

  // Real data for HomeScreen preview items from ActivityItem list
  List<HomeScreenItem> get _homeScreenItems {
    // Return empty list if no activity items provided
    if (widget.activityItems == null || widget.activityItems!.isEmpty) {
      return [];
    }

    // Convert ActivityItem to HomeScreenItem and take only the most recent 3-4 items
    return widget.activityItems!
        .take(4)
        .map((activityItem) => _convertActivityToHomeScreenItem(activityItem))
        .toList();
  }

  // Helper method to convert ActivityItem to HomeScreenItem
  HomeScreenItem _convertActivityToHomeScreenItem(ActivityItem activityItem) {
    // Map category to icon and color
    IconData icon;
    Color color;

    switch (activityItem.category.name.toLowerCase()) {
      case 'payment':
        icon = Icons.payment;
        color = Colors.blue;
        break;
      case 'invoice':
        icon = Icons.receipt_long;
        color = Colors.orange;
        break;
      case 'tax':
        icon = Icons.account_balance;
        color = Colors.green;
        break;
      default:
        icon = Icons.description;
        color = Colors.grey;
    }

    // Map status to localized text
    final l10n = AppLocalizations.of(context);
    String localizedStatus = l10n.getLocalizedStatus(activityItem.status);

    // Format date from createdAt
    String formattedDate;
    try {
      final DateTime dateTime = DateTime.parse(activityItem.createdAt);
      formattedDate =
          '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } catch (e) {
      formattedDate =
          activityItem.createdAt.substring(0, 10); // Fallback to first 10 chars
    }

    // Parse amount from price string
    double? amount;
    try {
      amount = double.parse(activityItem.price.replaceAll(',', ''));
    } catch (e) {
      amount = null;
    }

    return HomeScreenItem(
      id: activityItem.id.toString(),
      title: activityItem.title,
      description: activityItem.description.isNotEmpty
          ? activityItem.description
          : '${activityItem.category.name} - ${activityItem.buyerName}',
      icon: icon,
      color: color,
      amount: amount,
      status: localizedStatus,
      date: formattedDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.white,
          backgroundColor: Colors.white,
          elevation: 0,
          title: Text(
            AppLocalizations.of(context).dashboardApprover,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1.0),
            child: Container(
              color: Colors.grey.shade600,
              height: 1.0,
            ),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _refreshData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Account balance with no left/right padding to align to right edge
                      _buildAccountBalance(),
                      // Add padding for other content
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),
                            _buildFinancialSummaryCards(),
                            const SizedBox(height: 24),
                            Text(
                              AppLocalizations.of(context).financialOverview,
                              style: const TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            _buildFinancialChart(),
                            const SizedBox(height: 24),
                            _buildHomeScreenPreview(),
                            const SizedBox(height: 16),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ));
  }

  // Refresh data method
  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 1000));

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Open fullscreen chart
  void _openFullscreenChart() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FullscreenChartScreen(
          financialData: _financialData,
          selectedPeriod: _selectedPeriod,
          selectedMonth: _selectedMonth,
          selectedYear: _selectedYear,
          totalIncome: _totalIncome,
          totalExpense: _totalExpense,
          formatCurrency: _formatFullCurrency,
        ),
      ),
    );
  }

  // Calculate dynamic width for account balance card
  double _calculateBalanceCardWidth() {
    final formattedBalance = _formatFullCurrency(_accountBalance);
    final labelText = AppLocalizations.of(context).accountBalance;

    // Create TextPainter to measure balance amount width
    final balanceTextPainter = TextPainter(
      text: TextSpan(
        text: formattedBalance,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    // Create TextPainter to measure label width
    final labelTextPainter = TextPainter(
      text: TextSpan(
        text: labelText,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: Colors.white.withOpacity(0.9),
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    balanceTextPainter.layout();
    labelTextPainter.layout();

    // Calculate total width needed:
    // - Icon container: 18px icon + 8px padding * 2 = 34px
    // - Spacing between icon and text column: 10px
    // - Text width: maximum of label width and balance width
    // - Card padding: 20px * 2 = 40px
    // - Extra margin for comfort: 16px
    final iconWidth = 34.0; // 18px + 8px*2
    final spacing = 10.0;
    final maxTextWidth =
        math.max(balanceTextPainter.width, labelTextPainter.width);
    final cardPadding = 40.0; // 20px*2
    final extraMargin = 16.0;

    final calculatedWidth =
        iconWidth + spacing + maxTextWidth + cardPadding + extraMargin;

    // Apply constraints
    final screenWidth = MediaQuery.of(context).size.width;
    final minWidth = 140.0; // Increased minimum for label + amount
    final maxWidth = screenWidth * 0.8;

    return calculatedWidth.clamp(minWidth, maxWidth);
  }

  // Build account balance display
  Widget _buildAccountBalance() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(
          top: 16, bottom: 16, left: 16), // No right padding
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end, // Align to right edge
        children: [
          Container(
            width: _calculateBalanceCardWidth(),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
                topRight: Radius.zero,
                bottomRight: Radius.zero,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  spreadRadius: 1,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 10),

                // Label and Balance amount in vertical layout
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Label text
                    Text(
                      AppLocalizations.of(context).accountBalance,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    const SizedBox(height: 2),

                    // Balance amount
                    Text(
                      _formatFullCurrency(_accountBalance),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  // Build financial summary cards (Thu vào / Chi ra)
  Widget _buildFinancialSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            title: AppLocalizations.of(context).income,
            amount: _totalIncome,
            color: Colors.green,
            icon: Icons.trending_up,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            title: AppLocalizations.of(context).expense,
            amount: _totalExpense,
            color: Colors.red,
            icon: Icons.trending_down,
          ),
        ),
      ],
    );
  }

  // Build individual summary card
  Widget _buildSummaryCard({
    required String title,
    required double amount,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 10),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            _formatFullCurrency(amount),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // Build financial chart (Bar chart for Thu vào / Chi ra)
  Widget _buildFinancialChart() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart header with period and secondary selectors
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPeriodSelector(),
                    if (_selectedPeriod != 'Năm') ...[
                      const SizedBox(height: 12),
                      _buildSecondarySelector(),
                    ],
                  ],
                ),
              ),
              // Fullscreen button
              IconButton(
                onPressed: _openFullscreenChart,
                icon: const Icon(
                  Icons.fullscreen,
                  color: Colors.grey,
                  size: 24,
                ),
                tooltip: AppLocalizations.of(context).viewFullscreen,
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Chart
          SizedBox(
            height: 250,
            child: SfCartesianChart(
              primaryXAxis: CategoryAxis(
                majorGridLines: const MajorGridLines(width: 0),
                axisLine: const AxisLine(width: 0),
                labelStyle: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              primaryYAxis: NumericAxis(
                majorGridLines: MajorGridLines(
                  width: 1,
                  color: Colors.grey.withValues(alpha: 0.2),
                ),
                axisLine: const AxisLine(width: 0),
                isVisible: false, // Hide Y-axis labels
              ),
              plotAreaBorderWidth: 0,
              series: <CartesianSeries>[
                ColumnSeries<FinancialData, String>(
                  name: AppLocalizations.of(context).income,
                  dataSource: _financialData,
                  xValueMapper: (FinancialData data, _) => data.period,
                  yValueMapper: (FinancialData data, _) => data.income,
                  color: Colors.green,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                  width: 0.6,
                  spacing: 0.2,
                ),
                ColumnSeries<FinancialData, String>(
                  name: AppLocalizations.of(context).expense,
                  dataSource: _financialData,
                  xValueMapper: (FinancialData data, _) => data.period,
                  yValueMapper: (FinancialData data, _) => data.expense,
                  color: Colors.red,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                  width: 0.6,
                  spacing: 0.2,
                ),
              ],
              legend: Legend(
                isVisible: true,
                position: LegendPosition.bottom,
                textStyle: const TextStyle(fontSize: 12),
              ),
              tooltipBehavior: TooltipBehavior(
                enable: true,
                format: 'point.x: point.y',
                textStyle: const TextStyle(fontSize: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build period selector
  Widget _buildPeriodSelector() {
    final periods = [
      AppLocalizations.of(context).week,
      AppLocalizations.of(context).month,
      AppLocalizations.of(context).quarter,
      AppLocalizations.of(context).year
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: periods.map((period) {
          final isSelected = period == _getLocalizedPeriod(_selectedPeriod);
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedPeriod = _getInternalPeriod(period);
                // Reset secondary selectors when period changes
                if (period == AppLocalizations.of(context).week) {
                  // Default to most recent completed month
                  final currentMonth = DateTime.now().month;
                  _selectedMonth = currentMonth > 1 ? currentMonth - 1 : 1;
                } else if (period == AppLocalizations.of(context).month ||
                    period == AppLocalizations.of(context).quarter) {
                  _selectedYear = DateTime.now().year;
                }
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                period,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? Colors.white : Colors.grey[600],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // Build secondary selector (month/year dropdown)
  Widget _buildSecondarySelector() {
    switch (_selectedPeriod) {
      case 'Tuần':
        return _buildMonthSelector();
      case 'Tháng':
      case 'Quý':
        return _buildYearSelector();
      default:
        return const SizedBox.shrink();
    }
  }

  // Build month selector for weekly view
  Widget _buildMonthSelector() {
    final availableMonths = _availableMonths;

    // Handle case when no completed months are available (e.g., current month is January)
    if (availableMonths.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Text(
          AppLocalizations.of(context).noCompletedMonths,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    // Ensure selected month is valid for current available months
    if (!availableMonths.contains(_selectedMonth)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _selectedMonth =
              availableMonths.last; // Default to most recent completed month
        });
      });
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: availableMonths.contains(_selectedMonth)
              ? _selectedMonth
              : availableMonths.last,
          isDense: true,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[700],
            fontWeight: FontWeight.w500,
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            size: 16,
            color: Colors.grey[600],
          ),
          items: availableMonths.map((month) {
            return DropdownMenuItem<int>(
              value: month,
              child: Text(_getMonthName(month)),
            );
          }).toList(),
          onChanged: (int? newMonth) {
            if (newMonth != null) {
              setState(() {
                _selectedMonth = newMonth;
              });
            }
          },
        ),
      ),
    );
  }

  // Build year selector for monthly and quarterly view
  Widget _buildYearSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: _selectedYear,
          isDense: true,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[700],
            fontWeight: FontWeight.w500,
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            size: 16,
            color: Colors.grey[600],
          ),
          items: _availableYears.map((year) {
            return DropdownMenuItem<int>(
              value: year,
              child: Text(year.toString()),
            );
          }).toList(),
          onChanged: (int? newYear) {
            if (newYear != null) {
              setState(() {
                _selectedYear = newYear;
              });
            }
          },
        ),
      ),
    );
  }

  // Get localized month name
  String _getMonthName(int month) {
    final monthNames = [
      AppLocalizations.of(context).month1,
      AppLocalizations.of(context).month2,
      AppLocalizations.of(context).month3,
      AppLocalizations.of(context).month4,
      AppLocalizations.of(context).month5,
      AppLocalizations.of(context).month6,
      AppLocalizations.of(context).month7,
      AppLocalizations.of(context).month8,
      AppLocalizations.of(context).month9,
      AppLocalizations.of(context).month10,
      AppLocalizations.of(context).month11,
      AppLocalizations.of(context).month12
    ];
    return monthNames[month - 1];
  }

  // Get label format for chart axis
  String _getLabelFormat() {
    switch (_selectedPeriod) {
      case 'Năm':
        return '{value}M';
      case 'Quý':
        return '{value}M';
      default:
        return '{value}M';
    }
  }

  // Build HomeScreen preview list
  Widget _buildHomeScreenPreview() {
    // Show empty state if no activity items
    if (_homeScreenItems.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).noActivities,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context).recentActivitiesDescription,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context).recentActivities,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // Navigate to HomeScreen tab
                    if (widget.onNavigateToHome != null) {
                      widget.onNavigateToHome!();
                    }
                  },
                  child: Text(
                    AppLocalizations.of(context).viewAll,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // List items
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 20),
            itemCount: _homeScreenItems.length,
            separatorBuilder: (context, index) => const Divider(
              height: 1,
              color: Colors.grey,
              thickness: 0.5,
            ),
            itemBuilder: (context, index) {
              final item = _homeScreenItems[index];
              return _buildHomeScreenItem(item);
            },
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  // Build individual HomeScreen item
  Widget _buildHomeScreenItem(HomeScreenItem item) {
    Color statusColor;
    switch (item.status) {
      case 'Đã duyệt':
        statusColor = Colors.lightGreen[400]!; // Match activities_screen
        break;
      case 'Chờ duyệt':
        statusColor = Colors.lightBlue[300]!; // Match activities_screen
        break;
      case 'Chờ kế toán':
        statusColor = Colors.amber[400]!; // Match activities_screen
        break;
      case 'Từ chối':
        statusColor = Colors.red[400]!; // Match activities_screen
        break;
      case 'Nháp':
        statusColor = Colors.grey[300]!; // Match activities_screen
        break;
      case 'Hủy':
        statusColor = Colors.grey[700]!; // Match activities_screen
        break;
      default:
        statusColor = Colors.grey[300]!; // Match activities_screen
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.grey[50]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            spreadRadius: 0,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.25),
          width: 2,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // Navigate to HomeScreen tab when item is tapped
            if (widget.onNavigateToHome != null) {
              widget.onNavigateToHome!();
            }
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Header row with title and status
                Row(
                  children: [
                    // Title and date column - expanded to use full available space
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                              letterSpacing: -0.3,
                              height: 1.2,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: Colors.grey[500],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                item.date,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.grey[500],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Status badge with modern design
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 14,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: statusColor.withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _getLocalizedStatus(item.status),
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                              letterSpacing: 0.2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Description section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    item.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.4,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                const SizedBox(height: 16),

                // Bottom row with amount display - centered layout
                if (item.amount != null)
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            item.color.withOpacity(0.15),
                            item.color.withOpacity(0.08),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: item.color.withOpacity(0.2),
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.attach_money,
                            color: item.color,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            _formatFullCurrency(item.amount!),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w800,
                              color: item.color,
                              letterSpacing: -0.2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Fullscreen Chart Screen
class FullscreenChartScreen extends StatefulWidget {
  final List<FinancialData> financialData;
  final String selectedPeriod;
  final int selectedMonth;
  final int selectedYear;
  final double totalIncome;
  final double totalExpense;
  final String Function(double) formatCurrency;

  const FullscreenChartScreen({
    super.key,
    required this.financialData,
    required this.selectedPeriod,
    required this.selectedMonth,
    required this.selectedYear,
    required this.totalIncome,
    required this.totalExpense,
    required this.formatCurrency,
  });

  @override
  State<FullscreenChartScreen> createState() => _FullscreenChartScreenState();
}

class _FullscreenChartScreenState extends State<FullscreenChartScreen> {
  @override
  void initState() {
    super.initState();
    // Force landscape orientation
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    // Restore portrait orientation when leaving
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '${AppLocalizations.of(context).financialOverview} - ${widget.selectedPeriod}',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // Summary info in app bar
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${AppLocalizations.of(context).incomeLabel}: ${widget.formatCurrency(widget.totalIncome)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${AppLocalizations.of(context).expenseLabel}: ${widget.formatCurrency(widget.totalExpense)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SfCartesianChart(
          primaryXAxis: CategoryAxis(
            majorGridLines: const MajorGridLines(width: 0),
            axisLine: const AxisLine(width: 0),
            labelStyle: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          primaryYAxis: NumericAxis(
            majorGridLines: MajorGridLines(
              width: 1,
              color: Colors.grey.withValues(alpha: 0.2),
            ),
            axisLine: const AxisLine(width: 0),
            isVisible: false, // Hide Y-axis labels
          ),
          plotAreaBorderWidth: 0,
          series: <CartesianSeries>[
            ColumnSeries<FinancialData, String>(
              name: AppLocalizations.of(context).income,
              dataSource: widget.financialData,
              xValueMapper: (FinancialData data, _) => data.period,
              yValueMapper: (FinancialData data, _) => data.income,
              color: Colors.green,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
              width: 0.7,
              spacing: 0.2,
            ),
            ColumnSeries<FinancialData, String>(
              name: AppLocalizations.of(context).expense,
              dataSource: widget.financialData,
              xValueMapper: (FinancialData data, _) => data.period,
              yValueMapper: (FinancialData data, _) => data.expense,
              color: Colors.red,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
              width: 0.7,
              spacing: 0.2,
            ),
          ],
          legend: Legend(
            isVisible: true,
            position: LegendPosition.bottom,
            textStyle: const TextStyle(fontSize: 14),
          ),
          tooltipBehavior: TooltipBehavior(
            enable: true,
            format: 'point.x: point.y',
            textStyle: const TextStyle(fontSize: 14),
          ),
        ),
      ),
    );
  }
}
